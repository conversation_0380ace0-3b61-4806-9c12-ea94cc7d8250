"""
Тест функционала связи с куратором
"""
import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    StudentRepository, CuratorRepository, SubjectRepository, 
    UserRepository, GroupRepository, init_database
)


async def test_curator_contact():
    """Тест функционала связи с куратором"""
    print("🧪 Тестирование функционала связи с куратором...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        
        # Тестируем для студента Аружан Ахметова (группа М-1, Математика)
        student_telegram_id = 666777888
        print(f"\n👤 Тестируем для студента с Telegram ID: {student_telegram_id}")
        
        # Получаем студента
        student = await StudentRepository.get_by_telegram_id(student_telegram_id)
        if not student:
            print("❌ Студент не найден")
            return
        
        print(f"✅ Студент найден: {student.user.name}")
        
        # Получаем предметы студента
        subjects = []
        for group in student.groups:
            if group.subject and group.subject not in subjects:
                subjects.append(group.subject)
        
        print(f"📚 Предметы студента: {[s.name for s in subjects]}")
        
        # Тестируем для каждого предмета
        for subject in subjects:
            print(f"\n🔍 Ищем кураторов по предмету: {subject.name}")
            
            # Используем новый метод
            curators = await CuratorRepository.get_curators_for_student_subject(student.id, subject.id)
            
            if curators:
                print(f"✅ Найдено кураторов: {len(curators)}")
                for curator in curators:
                    print(f"   👤 {curator.user.name}")
                    
                    # Показываем группы куратора, которые пересекаются со студентом
                    student_groups_for_subject = [group for group in student.groups if group.subject_id == subject.id]
                    curator_groups_for_student = []

                    print(f"   🔍 Группы студента по предмету: {[g.name for g in student_groups_for_subject]}")
                    print(f"   🔍 Группы куратора: {[g.name for g in curator.groups]}")

                    for group in student_groups_for_subject:
                        for curator_group in curator.groups:
                            if group.id == curator_group.id:
                                curator_groups_for_student.append(group)

                    groups_text = ", ".join([group.name for group in curator_groups_for_student]) if curator_groups_for_student else "Нет общих групп"
                    print(f"   📚 Общие группы: {groups_text}")
                    
                    # Формируем telegram username
                    telegram_info = f"@{curator.user.name.lower().replace(' ', '_')}"
                    print(f"   📩 Telegram: {telegram_info}")
            else:
                print(f"❌ Кураторы по предмету {subject.name} не найдены")
        
        print("\n✅ Тест завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_curator_contact())
