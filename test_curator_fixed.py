"""
Тест исправленного функционала связи с куратором
"""
import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    StudentRepository, CuratorRepository, SubjectRepository, 
    UserRepository, GroupRepository, init_database
)


async def test_curator_contact_fixed():
    """Тест исправленного функционала связи с куратором"""
    print("🧪 Тестирование ИСПРАВЛЕННОГО функционала связи с куратором...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        
        # Тестируем для студента Аружан Ахметова (группа М-1, Математика)
        student_telegram_id = 666777888
        print(f"\n👤 Тестируем для студента с Telegram ID: {student_telegram_id}")
        
        # Получаем студента
        student = await StudentRepository.get_by_telegram_id(student_telegram_id)
        if not student:
            print("❌ Студент не найден")
            return
        
        print(f"✅ Студент найден: {student.user.name}")
        
        # Получаем ТОЛЬКО предметы студента из его групп (без хардкода!)
        subjects = []
        for group in student.groups:
            if group.subject and group.subject not in subjects:
                subjects.append(group.subject)
        
        print(f"📚 Предметы студента ИЗ БАЗЫ ДАННЫХ: {[s.name for s in subjects]}")
        
        # Тестируем для каждого предмета
        for subject in subjects:
            print(f"\n🔍 Ищем кураторов по предмету: {subject.name}")
            
            # Используем метод для получения кураторов
            curators = await CuratorRepository.get_curators_for_student_subject(student.id, subject.id)
            
            if curators:
                print(f"✅ Найдено кураторов: {len(curators)}")
                for curator in curators:
                    print(f"   👤 {curator.user.name}")
                    
                    # Показываем РЕАЛЬНУЮ ссылку на Telegram
                    telegram_link = f"tg://user?id={curator.user.telegram_id}"
                    print(f"   📩 РЕАЛЬНАЯ ссылка Telegram: {telegram_link}")
                    
                    # Показываем группы куратора, которые пересекаются со студентом
                    student_groups_for_subject = [group for group in student.groups if group.subject_id == subject.id]
                    curator_groups_for_student = []
                    
                    for group in student_groups_for_subject:
                        for curator_group in curator.groups:
                            if group.id == curator_group.id:
                                curator_groups_for_student.append(group)
                    
                    groups_text = ", ".join([group.name for group in curator_groups_for_student]) if curator_groups_for_student else "Нет общих групп"
                    print(f"   📚 Общие группы: {groups_text}")
                    
                    # Показываем как будет выглядеть в боте
                    print(f"   💬 В боте: [Открыть чат]({telegram_link})")
            else:
                print(f"❌ Кураторы по предмету {subject.name} не найдены")
        
        print("\n✅ Тест ИСПРАВЛЕННОГО функционала завершен успешно!")
        print("🔧 Исправления:")
        print("   ✅ Убран хардкод предметов - используются только из базы данных")
        print("   ✅ Исправлены Telegram ссылки - используется реальный telegram_id")
        print("   ✅ Добавлена поддержка Markdown для кликабельных ссылок")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_curator_contact_fixed())
