"""
Тест получения предметов ТОЛЬКО из базы данных с кураторами
"""
import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    StudentRepository, CuratorRepository, SubjectRepository, 
    UserRepository, GroupRepository, init_database
)


async def test_subjects_from_database():
    """Тест получения предметов ТОЛЬКО из базы данных"""
    print("🧪 Тестирование получения предметов ИЗ БАЗЫ ДАННЫХ...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        
        # Тестируем для разных студентов
        test_students = [
            666777888,  # Аружан Ахметова - группа М-1 (Математика)
            333444555,  # Муханбетжан Олжас - группы PY-1, М-2 (Python, Математика)
            888999000,  # Ерасыл Мухамедов - группа М-2 (Математика)
        ]
        
        for student_telegram_id in test_students:
            print(f"\n👤 Тестируем студента с Telegram ID: {student_telegram_id}")
            
            # Получаем студента
            student = await StudentRepository.get_by_telegram_id(student_telegram_id)
            if not student:
                print("❌ Студент не найден")
                continue
            
            print(f"✅ Студент: {student.user.name}")
            
            # Показываем ВСЕ группы студента
            print(f"📚 Все группы студента: {[g.name + ' (' + (g.subject.name if g.subject else 'Без предмета') + ')' for g in student.groups]}")
            
            # Получаем предметы студента, по которым есть кураторы (КАК В РЕАЛЬНОМ КОДЕ)
            subjects_with_curators = []
            
            for group in student.groups:
                if group.subject:
                    # Проверяем, есть ли кураторы по этому предмету для данного студента
                    curators = await CuratorRepository.get_curators_for_student_subject(student.id, group.subject.id)
                    if curators and group.subject not in subjects_with_curators:
                        subjects_with_curators.append(group.subject)
                        print(f"   ✅ Предмет {group.subject.name}: найдено {len(curators)} кураторов")
                    elif group.subject not in subjects_with_curators:
                        print(f"   ❌ Предмет {group.subject.name}: кураторы не найдены")
            
            print(f"🎯 ИТОГО предметов с кураторами: {[s.name for s in subjects_with_curators]}")
            
            if not subjects_with_curators:
                print("❌ По предметам студента кураторы не назначены")
        
        print("\n✅ Тест завершен!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_subjects_from_database())
