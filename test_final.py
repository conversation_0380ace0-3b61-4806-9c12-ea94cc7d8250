"""
Финальный тест функционала связи с куратором
"""
import asyncio
import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    StudentRepository, CuratorRepository, SubjectRepository, 
    UserRepository, GroupRepository, init_database
)


async def test_final_curator_contact():
    """Финальный тест функционала связи с куратором"""
    print("🧪 ФИНАЛЬНЫЙ тест функционала связи с куратором...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        
        # Тестируем для студента
        student_telegram_id = 666777888
        print(f"\n👤 Тестируем для студента с Telegram ID: {student_telegram_id}")
        
        # Получаем студента
        student = await StudentRepository.get_by_telegram_id(student_telegram_id)
        if not student:
            print("❌ Студент не найден")
            return
        
        print(f"✅ Студент: {student.user.name}")
        
        # Получаем предметы с кураторами (как в реальном коде)
        subjects_with_curators = []
        
        for group in student.groups:
            if group.subject:
                curators = await CuratorRepository.get_curators_for_student_subject(student.id, group.subject.id)
                if curators and group.subject not in subjects_with_curators:
                    subjects_with_curators.append(group.subject)
        
        print(f"📚 Предметы с кураторами: {[s.name for s in subjects_with_curators]}")
        
        # Симулируем клавиатуру (БЕЗ ЭМОДЗИ!)
        print("\n⌨️ Кнопки в клавиатуре:")
        sorted_subjects = sorted(subjects_with_curators, key=lambda s: s.name)
        for subject in sorted_subjects:
            print(f"   [{subject.name}] -> curator_{subject.id}")
        
        # Тестируем выбор предмета
        if subjects_with_curators:
            test_subject = subjects_with_curators[0]
            print(f"\n🔍 Тестируем выбор предмета: {test_subject.name}")
            
            curators = await CuratorRepository.get_curators_for_student_subject(student.id, test_subject.id)
            print(f"✅ Найдено кураторов: {len(curators)}")
            
            for curator in curators:
                telegram_link = f"tg://user?id={curator.user.telegram_id}"
                print(f"   👤 {curator.user.name}")
                print(f"   📩 Ссылка: {telegram_link}")
                print(f"   💬 В боте: [Открыть чат]({telegram_link})")
        
        print("\n✅ ФИНАЛЬНЫЙ тест завершен успешно!")
        print("🎯 Результат:")
        print("   ✅ Предметы берутся ТОЛЬКО из базы данных")
        print("   ✅ Показываются ТОЛЬКО предметы с кураторами")
        print("   ✅ БЕЗ эмодзи - только названия предметов как в базе")
        print("   ✅ Реальные Telegram ссылки по telegram_id")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_final_curator_contact())
