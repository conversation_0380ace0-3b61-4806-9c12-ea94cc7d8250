# 📊 Мониторинг производительности Telegram бота

Базовый мониторинг производительности для анализа работы бота при одновременной работе 50+ учеников.

## 🚀 Быстрый старт

### 1. Установка зависимостей
```bash
pip install -r requirements.txt
```

### 2. Встроенный мониторинг
Middleware автоматически собирает метрики при работе бота:
- Время ответа на каждый запрос
- Использование памяти и CPU
- Количество одновременных запросов

### 3. Просмотр статистики
```bash
curl https://edubot.schoolpro.kz/stats
```

## 🛠️ Встроенный мониторинг

### Автоматический сбор метрик
Middleware `PerformanceMiddleware` автоматически отслеживает:

- **Время ответа** на каждый запрос (message/callback)
- **Использование памяти** процесса бота
- **CPU нагрузку** процесса
- **Количество одновременных запросов**
- **Медленные запросы** (>1 секунды) логируются

### Доступ к статистике
- **Endpoint**: `https://edubot.schoolpro.kz/stats`
- **Формат**: JSON с текущими метриками
- **Обновление**: В реальном времени

### Логирование
- Медленные запросы автоматически логируются в файл
- Метрики сохраняются в Redis (если доступен)
- Системная информация обновляется при каждом запросе

## 📈 Анализ производительности

### Текущие ограничения VPS
- **CPU**: 1 ядро (критично для 50+ пользователей)
- **RAM**: ~1GB (может вызывать swap)
- **Диск**: NVMe (быстрый)

### Мониторинг в реальном времени
```bash
# Просмотр текущей статистики
curl https://edubot.schoolpro.kz/stats

# Пример ответа:
{
  "active_requests": 2,
  "max_concurrent_requests": 5,
  "avg_response_time": 0.234,
  "min_response_time": 0.045,
  "max_response_time": 1.234,
  "total_requests": 156,
  "memory_usage_mb": 45.2,
  "cpu_percent": 12.5
}
```

### Анализ логов
```bash
# Поиск медленных запросов
grep "Медленный запрос" logs/bot_*.log

# Мониторинг ошибок
grep "ERROR" logs/bot_*.log
```

## 🔍 Ключевые метрики для вашего VPS

### Критические показатели (1 ядро, 1GB RAM)
- **Время ответа**: < 2 секунды (норма), > 5 секунд (критично)
- **Память процесса**: < 200MB (норма), > 400MB (критично)
- **CPU процесса**: < 50% (норма), > 80% (критично)
- **Одновременные запросы**: до 10-15 без деградации

### Признаки проблем
- **Swap активность**: Система использует диск вместо RAM
- **Высокий load average**: > 1.0 на одноядерной системе
- **Медленные запросы**: > 3 секунд регулярно
- **Ошибки памяти**: OOM killer активность

### Мониторинг системы
```bash
# Проверка использования памяти
free -h

# Проверка load average
uptime

# Проверка swap
swapon --show
```

## 🚨 Потенциальные проблемы на вашем VPS

### 1. Ограничения ресурсов
- **1 ядро CPU**: Узкое место для 50+ одновременных пользователей
- **1GB RAM**: Может не хватить при пиковых нагрузках
- **Решение**: Оптимизация кода, кэширование, возможно upgrade VPS

### 2. Обработчики домашних заданий
- **Проблема**: Множественные запросы к БД на каждого студента
- **Решение**: Batch запросы, connection pooling

### 3. Redis и PostgreSQL
- **Проблема**: Два сервиса на одном VPS потребляют память
- **Решение**: Настройка лимитов памяти, мониторинг

### 4. Webhook обработка
- **Проблема**: Медленная обработка может вызвать таймауты Telegram
- **Решение**: Асинхронная обработка, очереди задач

## 📊 Анализ результатов

### Flame Graph (py-spy)
- **Красные блоки**: Горячие функции (много времени)
- **Широкие блоки**: Часто вызываемые функции
- **Глубокие стеки**: Сложные вызовы

### cProfile статистика
- **cumtime**: Общее время включая подфункции
- **tottime**: Время только этой функции
- **ncalls**: Количество вызовов

### Memory Profile
- **RSS**: Физическая память
- **VMS**: Виртуальная память
- **Рост**: Потенциальные утечки

## 🔧 Оптимизации

### 1. База данных
```python
# Используйте connection pooling
engine = create_async_engine(
    DATABASE_URL, 
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

### 2. Redis кэширование
```python
# Кэшируйте часто используемые данные
@cache_result(ttl=300)
async def get_user_role(user_id: int):
    # Запрос к БД
    pass
```

### 3. Асинхронность
```python
# Используйте gather для параллельных операций
results = await asyncio.gather(
    get_courses(),
    get_subjects(),
    get_lessons()
)
```

## 📝 Логирование производительности

### Автоматическое логирование
- Медленные запросы (> 1 сек) логируются автоматически
- Метрики сохраняются в Redis
- Системная статистика собирается каждые 30 сек

### Ручное логирование
```python
import time
from middlewares.performance_middleware import db_monitor

start_time = time.time()
# Ваш код
execution_time = time.time() - start_time
await db_monitor.log_query("SELECT * FROM users", execution_time)
```

## 🎯 Реалистичные целевые показатели

### Для вашего VPS (1 ядро, 1GB RAM):
- **Время ответа**: < 3 сек (95 перцентиль)
- **Пропускная способность**: 5-10 запросов/сек
- **Использование памяти процесса**: < 300MB
- **CPU процесса**: < 70%
- **Одновременные пользователи**: 10-20 комфортно

### Для 50 студентов (потребует оптимизации):
- **Время прохождения теста**: 10-15 минут
- **Ошибки**: < 5%
- **Возможные таймауты**: Ожидаемы при пиках

### Рекомендации по upgrade:
- **2 ядра CPU + 2GB RAM**: Комфортно для 50 пользователей
- **4 ядра CPU + 4GB RAM**: Запас для роста до 100+ пользователей

## 🔄 Непрерывный мониторинг

### Production мониторинг
1. **Grafana + Prometheus**: Для визуализации метрик
2. **Sentry**: Для отслеживания ошибок
3. **New Relic**: Для APM мониторинга

### Алерты
- CPU > 80% в течение 5 минут
- Память > 90% в течение 2 минут
- Время ответа > 3 сек для 10+ запросов
- Ошибки > 5% в течение минуты

## 📞 Диагностика проблем

При медленной работе бота:

1. **Проверьте статистику**: `curl https://edubot.schoolpro.kz/stats`
2. **Проверьте логи**: `grep "Медленный запрос" logs/bot_*.log`
3. **Проверьте системные ресурсы**: `free -h && uptime`
4. **Проверьте процессы**: `ps aux | grep python`

## 🚀 Следующие шаги оптимизации

1. **Установить psutil**: `pip install -r requirements.txt`
2. **Протестировать мониторинг**: Проверить endpoint `/stats`
3. **Создать нагрузочный тест**: Симуляция 10-50 пользователей
4. **Анализ узких мест**: По результатам мониторинга
5. **Оптимизация**: Connection pooling, кэширование, async операции
